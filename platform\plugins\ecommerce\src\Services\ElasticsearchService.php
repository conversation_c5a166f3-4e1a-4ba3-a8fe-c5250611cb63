<?php

namespace Botble\Ecommerce\Services;

use Botble\Ecommerce\Models\Product;
use Botble\Ecommerce\Models\ProductVariation;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use <PERSON><PERSON>\Scout\Builder;

class ElasticsearchService
{
    /**
     * Search products using Elasticsearch
     */
    public function searchProducts(Request $request): LengthAwarePaginator
    {
        $query = $request->input('q', '');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 12);
        $sortBy = $request->input('sort_by', 'relevance');
        $minPrice = $request->input('min_price');
        $maxPrice = $request->input('max_price');
        $brandIds = $request->input('brands', []);
        $categoryIds = $request->input('categories', []);
        $tagIds = $request->input('tags', []);
        $attributes = $request->input('attributes', []);
        $inStock = $request->input('in_stock', false);
        $featured = $request->input('featured', false);

        // Start with base search
        $searchBuilder = Product::search($query);

        // Apply filters
        $searchBuilder = $this->applyFilters($searchBuilder, [
            'min_price' => $minPrice,
            'max_price' => $maxPrice,
            'brand_ids' => $brandIds,
            'category_ids' => $categoryIds,
            'tag_ids' => $tagIds,
            'attributes' => $attributes,
            'in_stock' => $inStock,
            'featured' => $featured,
        ]);

        // Apply sorting
        $searchBuilder = $this->applySorting($searchBuilder, $sortBy);

        // Execute search with pagination
        $results = $searchBuilder->paginate($perPage, 'page', $page);

        return $results;
    }

    /**
     * Search product variations using Elasticsearch
     */
    public function searchProductVariations(Request $request): LengthAwarePaginator
    {
        $query = $request->input('q', '');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 12);
        $sortBy = $request->input('sort_by', 'relevance');
        $configurableProductId = $request->input('configurable_product_id');

        // Start with base search
        $searchBuilder = ProductVariation::search($query);

        // Filter by configurable product if specified
        if ($configurableProductId) {
            $searchBuilder->where('configurable_product_id', $configurableProductId);
        }

        // Apply sorting
        $searchBuilder = $this->applySorting($searchBuilder, $sortBy);

        // Execute search with pagination
        $results = $searchBuilder->paginate($perPage, 'page', $page);

        return $results;
    }

    /**
     * Get search suggestions
     */
    public function getSearchSuggestions(string $query, int $limit = 10): array
    {
        if (empty($query) || strlen($query) < 2) {
            return [];
        }

        // Search for product names that match the query
        $products = Product::search($query)
            ->where('status', 'published')
            ->where('is_variation', false)
            ->take($limit)
            ->get(['id', 'name', 'sku', 'price', 'image']);

        return $products->map(function ($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'price' => $product->price,
                'image' => $product->image,
                'url' => route('products.show', $product->slug ?? $product->id),
            ];
        })->toArray();
    }

    /**
     * Apply filters to search builder
     */
    protected function applyFilters(Builder $searchBuilder, array $filters): Builder
    {
        // Price range filter
        if (!empty($filters['min_price']) || !empty($filters['max_price'])) {
            $priceFilter = [];
            if (!empty($filters['min_price'])) {
                $priceFilter['gte'] = (float) $filters['min_price'];
            }
            if (!empty($filters['max_price'])) {
                $priceFilter['lte'] = (float) $filters['max_price'];
            }
            $searchBuilder->whereRaw([
                'range' => [
                    'price' => $priceFilter
                ]
            ]);
        }

        // Brand filter
        if (!empty($filters['brand_ids']) && is_array($filters['brand_ids'])) {
            $searchBuilder->whereIn('brand_id', $filters['brand_ids']);
        }

        // Category filter
        if (!empty($filters['category_ids']) && is_array($filters['category_ids'])) {
            $searchBuilder->whereRaw([
                'nested' => [
                    'path' => 'categories',
                    'query' => [
                        'terms' => [
                            'categories.id' => $filters['category_ids']
                        ]
                    ]
                ]
            ]);
        }

        // Tag filter
        if (!empty($filters['tag_ids']) && is_array($filters['tag_ids'])) {
            $searchBuilder->whereRaw([
                'nested' => [
                    'path' => 'tags',
                    'query' => [
                        'terms' => [
                            'tags.id' => $filters['tag_ids']
                        ]
                    ]
                ]
            ]);
        }

        // Attributes filter
        if (!empty($filters['attributes']) && is_array($filters['attributes'])) {
            foreach ($filters['attributes'] as $attributeId => $values) {
                if (!empty($values) && is_array($values)) {
                    $searchBuilder->whereRaw([
                        'nested' => [
                            'path' => 'attributes',
                            'query' => [
                                'bool' => [
                                    'must' => [
                                        ['term' => ['attributes.id' => $attributeId]],
                                        ['terms' => ['attributes.value.keyword' => $values]]
                                    ]
                                ]
                            ]
                        ]
                    ]);
                }
            }
        }

        // Stock filter
        if (!empty($filters['in_stock'])) {
            $searchBuilder->where('stock_status', 'in_stock');
        }

        // Featured filter
        if (!empty($filters['featured'])) {
            $searchBuilder->where('is_featured', true);
        }

        // Always filter published products
        $searchBuilder->where('status', 'published');

        return $searchBuilder;
    }

    /**
     * Apply sorting to search builder
     */
    protected function applySorting(Builder $searchBuilder, string $sortBy): Builder
    {
        switch ($sortBy) {
            case 'price_asc':
                $searchBuilder->orderBy('price', 'asc');
                break;
            case 'price_desc':
                $searchBuilder->orderBy('price', 'desc');
                break;
            case 'name_asc':
                $searchBuilder->orderBy('name.keyword', 'asc');
                break;
            case 'name_desc':
                $searchBuilder->orderBy('name.keyword', 'desc');
                break;
            case 'created_at_desc':
            case 'newest':
                $searchBuilder->orderBy('created_at', 'desc');
                break;
            case 'created_at_asc':
            case 'oldest':
                $searchBuilder->orderBy('created_at', 'asc');
                break;
            case 'featured':
                $searchBuilder->orderBy('is_featured', 'desc')
                             ->orderBy('created_at', 'desc');
                break;
            case 'relevance':
            default:
                // Elasticsearch handles relevance scoring by default
                break;
        }

        return $searchBuilder;
    }

    /**
     * Get aggregations for filters
     */
    public function getAggregations(string $query = ''): array
    {
        // This would require custom Elasticsearch queries
        // For now, we'll return empty array and implement later if needed
        return [
            'brands' => [],
            'categories' => [],
            'price_ranges' => [],
            'attributes' => [],
        ];
    }
}
